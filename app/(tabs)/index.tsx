import NFCTerminal from "@/components/NFCTerminal";
import { DEFAULT_TERMINALS, Terminal } from "@/types/student";
import React, { useState } from "react";
import { StyleSheet, View } from "react-native";

export default function HomeScreen() {
  const [selectedTerminal, setSelectedTerminal] = useState<Terminal>(
    DEFAULT_TERMINALS[0]
  );

  return (
    <View style={styles.container}>
      <NFCTerminal
        selectedTerminal={selectedTerminal}
        onTerminalChange={setSelectedTerminal}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
