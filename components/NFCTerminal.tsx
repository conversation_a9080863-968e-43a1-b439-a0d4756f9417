import {
  DEFAULT_TERMINALS,
  StudentData,
  Terminal,
  Transaction,
} from "@/types/student";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TransactionLogger } from "@/utils/nfcUtils";
import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import NfcManager, { Ndef, NfcTech } from "react-native-nfc-manager";

interface NFCTerminalProps {
  selectedTerminal: Terminal;
  onTerminalChange: (terminal: Terminal) => void;
}

const NFCTerminal: React.FC<NFCTerminalProps> = ({
  selectedTerminal,
  onTerminalChange,
}) => {
  const [isSupported, setIsSupported] = useState(false);
  const [isEnabled, setIsEnabled] = useState(false);
  const [studentData, setStudentData] = useState<StudentData | null>(null);
  const [isScanning, setIsScanning] = useState(false);
  const [isWriting, setIsWriting] = useState(false);
  const [lastTransaction, setLastTransaction] = useState<Transaction | null>(
    null
  );
  const [todaysTransactions, setTodaysTransactions] = useState<Transaction[]>(
    []
  );

  useEffect(() => {
    const initNfc = async () => {
      try {
        const supported = await NfcManager.isSupported();
        setIsSupported(supported);

        if (supported) {
          await NfcManager.start();
          const enabled = await NfcManager.isEnabled();
          setIsEnabled(enabled);
        }
      } catch (error) {
        console.warn("NFC initialization error:", error);
      }
    };

    initNfc();
    loadTodaysTransactions();

    return () => {
      NfcManager.setEventListener("", null);
      NfcManager.stop();
    };
  }, []);

  const loadTodaysTransactions = async () => {
    const transactions = await TransactionLogger.getTodaysTransactions();
    setTodaysTransactions(transactions);
  };

  const readStudentCard = async (): Promise<StudentData> => {
    await NfcManager.requestTechnology(NfcTech.Ndef);
    const tag = await NfcManager.getTag();

    if (!tag.ndefMessage || tag.ndefMessage.length === 0) {
      throw new Error("No NDEF message found on card");
    }

    const record = tag.ndefMessage[0];
    const payload = record.payload;

    // Remove the language code prefix for text records
    const languageCodeLength = payload[0];
    const text = payload.slice(languageCodeLength + 1);
    const studentDataString = String.fromCharCode(...text);

    return NFCDataHandler.decodeStudentData(studentDataString);
  };

  const writeStudentCard = async (data: StudentData): Promise<void> => {
    const dataString = NFCDataHandler.encodeStudentData(data);
    const bytes = [...Buffer.from(dataString, "utf8")];

    // Create NDEF text record
    const textRecord = Ndef.text.encodeText(dataString);
    const ndefMessage = [textRecord];

    await NfcManager.writeNdefMessage(ndefMessage);
  };

  const handleCardTap = async () => {
    try {
      setIsScanning(true);
      setStudentData(null);
      setLastTransaction(null);

      // Read current data from card
      const currentData = await readStudentCard();

      if (!NFCDataHandler.validateStudentData(currentData)) {
        throw new Error("Invalid student data on card");
      }

      setStudentData(currentData);

      // Check if balance is sufficient
      if (currentData.balance < selectedTerminal.transaction_cost) {
        Alert.alert(
          "Insufficient Balance",
          `Current balance: $${currentData.balance.toFixed(
            2
          )}\nRequired: $${selectedTerminal.transaction_cost.toFixed(2)}`,
          [{ text: "OK", onPress: () => NfcManager.cancelTechnologyRequest() }]
        );
        return;
      }

      // Show confirmation dialog
      Alert.alert(
        "Confirm Transaction",
        `Student: ${
          currentData.name
        }\nCurrent Balance: $${currentData.balance.toFixed(
          2
        )}\nTransaction Cost: $${selectedTerminal.transaction_cost.toFixed(
          2
        )}\nNew Balance: $${(
          currentData.balance - selectedTerminal.transaction_cost
        ).toFixed(2)}`,
        [
          {
            text: "Cancel",
            onPress: () => NfcManager.cancelTechnologyRequest(),
            style: "cancel",
          },
          {
            text: "Confirm",
            onPress: () => processTransaction(currentData),
          },
        ]
      );
    } catch (error) {
      console.error("Error reading card:", error);
      Alert.alert("Error", `Failed to read card: ${error.message}`);
      NfcManager.cancelTechnologyRequest();
    } finally {
      setIsScanning(false);
    }
  };

  const processTransaction = async (currentData: StudentData) => {
    try {
      setIsWriting(true);

      // Process the transaction
      const { updatedData, transaction } = NFCDataHandler.processTransaction(
        currentData,
        selectedTerminal.transaction_cost,
        selectedTerminal.id
      );

      // Write updated data back to card
      await writeStudentCard(updatedData);

      // Log transaction locally
      await TransactionLogger.logTransaction(transaction);

      setStudentData(updatedData);
      setLastTransaction(transaction);
      await loadTodaysTransactions();

      Alert.alert(
        "Transaction Successful",
        `Amount deducted: $${selectedTerminal.transaction_cost.toFixed(
          2
        )}\nNew balance: $${updatedData.balance.toFixed(2)}`,
        [{ text: "OK" }]
      );
    } catch (error) {
      console.error("Error processing transaction:", error);
      Alert.alert("Transaction Failed", `Error: ${error.message}`);
    } finally {
      setIsWriting(false);
      NfcManager.cancelTechnologyRequest();
    }
  };

  const writeNewStudentCard = async () => {
    Alert.prompt(
      "Create New Student Card",
      "Enter student details (format: ID,Name,Balance)",
      (input) => {
        if (!input) return;

        const parts = input.split(",");
        if (parts.length !== 3) {
          Alert.alert("Error", "Please use format: ID,Name,Balance");
          return;
        }

        const [id, name, balanceStr] = parts.map((p) => p.trim());
        const balance = parseFloat(balanceStr);

        if (isNaN(balance) || balance < 0) {
          Alert.alert("Error", "Invalid balance amount");
          return;
        }

        const newStudentData: StudentData = {
          student_id: id,
          name: name,
          balance: balance,
          txn_count: 0,
        };

        createNewCard(newStudentData);
      },
      "plain-text",
      "S12345,John Doe,50.00"
    );
  };

  const createNewCard = async (data: StudentData) => {
    try {
      setIsWriting(true);
      await NfcManager.requestTechnology(NfcTech.Ndef);
      await writeStudentCard(data);

      setStudentData(data);
      Alert.alert("Success", "New student card created successfully!");
    } catch (error) {
      console.error("Error creating card:", error);
      Alert.alert("Error", `Failed to create card: ${error.message}`);
    } finally {
      setIsWriting(false);
      NfcManager.cancelTechnologyRequest();
    }
  };

  if (!isSupported) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>
          NFC is not supported on this device
        </Text>
      </View>
    );
  }

  if (!isEnabled) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>NFC is not enabled</Text>
        <Text style={styles.infoText}>
          Please enable NFC in your device settings
        </Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      {/* Terminal Selection */}
      <View style={styles.terminalSection}>
        <Text style={styles.sectionTitle}>Select Terminal</Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {DEFAULT_TERMINALS.map((terminal) => (
            <TouchableOpacity
              key={terminal.id}
              style={[
                styles.terminalButton,
                selectedTerminal.id === terminal.id &&
                  styles.terminalButtonSelected,
              ]}
              onPress={() => onTerminalChange(terminal)}>
              <Text
                style={[
                  styles.terminalButtonText,
                  selectedTerminal.id === terminal.id &&
                    styles.terminalButtonTextSelected,
                ]}>
                {terminal.name}
              </Text>
              <Text
                style={[
                  styles.terminalCost,
                  selectedTerminal.id === terminal.id &&
                    styles.terminalCostSelected,
                ]}>
                ${terminal.transaction_cost.toFixed(2)}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Current Terminal Info */}
      <View style={styles.currentTerminalSection}>
        <Text style={styles.currentTerminalTitle}>
          Current Terminal: {selectedTerminal.name}
        </Text>
        <Text style={styles.currentTerminalCost}>
          Transaction Cost: ${selectedTerminal.transaction_cost.toFixed(2)}
        </Text>
      </View>

      {/* Action Buttons */}
      <View style={styles.buttonSection}>
        <TouchableOpacity
          style={[
            styles.button,
            styles.primaryButton,
            (isScanning || isWriting) && styles.buttonDisabled,
          ]}
          onPress={handleCardTap}
          disabled={isScanning || isWriting}>
          {isScanning ? (
            <ActivityIndicator color="white" />
          ) : (
            <Text style={styles.buttonText}>Tap Student Card</Text>
          )}
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.button,
            styles.secondaryButton,
            (isScanning || isWriting) && styles.buttonDisabled,
          ]}
          onPress={writeNewStudentCard}
          disabled={isScanning || isWriting}>
          {isWriting ? (
            <ActivityIndicator color="#007AFF" />
          ) : (
            <Text style={[styles.buttonText, styles.secondaryButtonText]}>
              Create New Card
            </Text>
          )}
        </TouchableOpacity>
      </View>

      {/* Student Data Display */}
      {studentData && (
        <View style={styles.dataSection}>
          <Text style={styles.sectionTitle}>Student Information</Text>
          <View style={styles.dataCard}>
            <Text style={styles.dataLabel}>
              ID: <Text style={styles.dataValue}>{studentData.student_id}</Text>
            </Text>
            <Text style={styles.dataLabel}>
              Name: <Text style={styles.dataValue}>{studentData.name}</Text>
            </Text>
            <Text style={styles.dataLabel}>
              Balance:{" "}
              <Text style={styles.dataValue}>
                ${studentData.balance.toFixed(2)}
              </Text>
            </Text>
            <Text style={styles.dataLabel}>
              Total Transactions:{" "}
              <Text style={styles.dataValue}>{studentData.txn_count}</Text>
            </Text>
          </View>
        </View>
      )}

      {/* Last Transaction */}
      {lastTransaction && (
        <View style={styles.dataSection}>
          <Text style={styles.sectionTitle}>Last Transaction</Text>
          <View style={styles.dataCard}>
            <Text style={styles.dataLabel}>
              Amount:{" "}
              <Text style={styles.dataValue}>
                -${lastTransaction.amount_deducted.toFixed(2)}
              </Text>
            </Text>
            <Text style={styles.dataLabel}>
              Time:{" "}
              <Text style={styles.dataValue}>
                {new Date(lastTransaction.timestamp).toLocaleString()}
              </Text>
            </Text>
            <Text style={styles.dataLabel}>
              Terminal:{" "}
              <Text style={styles.dataValue}>
                {lastTransaction.terminal_id}
              </Text>
            </Text>
          </View>
        </View>
      )}

      {/* Today's Transactions Summary */}
      <View style={styles.dataSection}>
        <Text style={styles.sectionTitle}>Today's Summary</Text>
        <View style={styles.dataCard}>
          <Text style={styles.dataLabel}>
            Total Transactions:{" "}
            <Text style={styles.dataValue}>{todaysTransactions.length}</Text>
          </Text>
          <Text style={styles.dataLabel}>
            Total Amount:{" "}
            <Text style={styles.dataValue}>
              $
              {todaysTransactions
                .reduce((sum, t) => sum + t.amount_deducted, 0)
                .toFixed(2)}
            </Text>
          </Text>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
    padding: 16,
  },
  terminalSection: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 12,
    color: "#333",
  },
  terminalButton: {
    backgroundColor: "white",
    padding: 16,
    borderRadius: 8,
    marginRight: 12,
    minWidth: 120,
    alignItems: "center",
    borderWidth: 2,
    borderColor: "#e0e0e0",
  },
  terminalButtonSelected: {
    borderColor: "#007AFF",
    backgroundColor: "#007AFF",
  },
  terminalButtonText: {
    fontSize: 14,
    fontWeight: "bold",
    color: "#333",
  },
  terminalButtonTextSelected: {
    color: "white",
  },
  terminalCost: {
    fontSize: 12,
    color: "#666",
    marginTop: 4,
  },
  terminalCostSelected: {
    color: "white",
  },
  currentTerminalSection: {
    backgroundColor: "white",
    padding: 16,
    borderRadius: 8,
    marginBottom: 20,
  },
  currentTerminalTitle: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 4,
  },
  currentTerminalCost: {
    fontSize: 14,
    color: "#666",
  },
  buttonSection: {
    marginBottom: 20,
  },
  button: {
    padding: 16,
    borderRadius: 8,
    alignItems: "center",
    marginBottom: 12,
  },
  primaryButton: {
    backgroundColor: "#007AFF",
  },
  secondaryButton: {
    backgroundColor: "white",
    borderWidth: 2,
    borderColor: "#007AFF",
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: "bold",
    color: "white",
  },
  secondaryButtonText: {
    color: "#007AFF",
  },
  dataSection: {
    marginBottom: 20,
  },
  dataCard: {
    backgroundColor: "white",
    padding: 16,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: "#007AFF",
  },
  dataLabel: {
    fontSize: 14,
    color: "#666",
    marginBottom: 8,
  },
  dataValue: {
    color: "#333",
    fontWeight: "bold",
  },
  errorText: {
    fontSize: 18,
    color: "#FF3B30",
    textAlign: "center",
    marginBottom: 10,
  },
  infoText: {
    fontSize: 14,
    color: "#666",
    textAlign: "center",
  },
});

export default NFCTerminal;
