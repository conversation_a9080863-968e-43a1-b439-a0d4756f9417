import { StudentData } from "@/types/student";
import { NFCDataHandler } from "@/utils/nfcUtils";
import React, { useState } from "react";
import {
  ActivityIndicator,
  Alert,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import NfcManager, { NfcTech } from "react-native-nfc-manager";

const StudentCardManager: React.FC = () => {
  const [studentId, setStudentId] = useState("");
  const [studentName, setStudentName] = useState("");
  const [initialBalance, setInitialBalance] = useState("");
  const [isWriting, setIsWriting] = useState(false);

  const validateForm = (): boolean => {
    if (!studentId.trim()) {
      Alert.alert("Error", "Student ID is required");
      return false;
    }
    if (!studentName.trim()) {
      Alert.alert("Error", "Student name is required");
      return false;
    }
    const balance = parseFloat(initialBalance);
    if (isNaN(balance) || balance < 0) {
      Alert.alert("Error", "Please enter a valid balance (minimum 0.00)");
      return false;
    }
    return true;
  };

  const createStudentCard = async () => {
    if (!validateForm()) return;

    const studentData: StudentData = {
      student_id: studentId.trim(),
      name: studentName.trim(),
      balance: parseFloat(initialBalance),
      txn_count: 0,
    };

    try {
      setIsWriting(true);

      // Request NFC technology
      await NfcManager.requestTechnology(NfcTech.Ndef);

      // Encode and write student data
      const dataString = NFCDataHandler.encodeStudentData(studentData);
      const bytes = [...Buffer.from(dataString, "utf8")];

      // Create NDEF text record
      const textRecord = {
        tnf: 1, // TNF_WELL_KNOWN
        type: [0x54], // 'T' for text
        payload: [0x02, 0x65, 0x6e, ...bytes], // Language code 'en' + data
      };

      await NfcManager.writeNdefMessage([textRecord]);

      Alert.alert(
        "Success!",
        `Student card created successfully!\n\nStudent ID: ${
          studentData.student_id
        }\nName: ${studentData.name}\nBalance: $${studentData.balance.toFixed(
          2
        )}`,
        [
          {
            text: "Create Another",
            onPress: () => {
              setStudentId("");
              setStudentName("");
              setInitialBalance("");
            },
          },
          { text: "Done" },
        ]
      );
    } catch (error) {
      console.error("Error creating student card:", error);
      Alert.alert("Error", `Failed to create student card: ${error.message}`);
    } finally {
      setIsWriting(false);
      NfcManager.cancelTechnologyRequest();
    }
  };

  const generateSampleData = () => {
    const sampleStudents = [
      { id: "S001", name: "John Doe", balance: "50.00" },
      { id: "S002", name: "Jane Smith", balance: "75.50" },
      { id: "S003", name: "Mike Johnson", balance: "100.00" },
      { id: "S004", name: "Sarah Wilson", balance: "25.75" },
      { id: "S005", name: "Alex Brown", balance: "60.25" },
    ];

    const randomStudent =
      sampleStudents[Math.floor(Math.random() * sampleStudents.length)];
    setStudentId(randomStudent.id);
    setStudentName(randomStudent.name);
    setInitialBalance(randomStudent.balance);
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Create New Student Card</Text>
        <Text style={styles.subtitle}>
          Fill in the student details and tap a blank NFC card to write the data
        </Text>
      </View>

      <View style={styles.form}>
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Student ID *</Text>
          <TextInput
            style={styles.input}
            value={studentId}
            onChangeText={setStudentId}
            placeholder="e.g., S12345"
            maxLength={20}
            autoCapitalize="characters"
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Student Name *</Text>
          <TextInput
            style={styles.input}
            value={studentName}
            onChangeText={setStudentName}
            placeholder="e.g., John Doe"
            maxLength={50}
            autoCapitalize="words"
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Initial Balance *</Text>
          <TextInput
            style={styles.input}
            value={initialBalance}
            onChangeText={setInitialBalance}
            placeholder="e.g., 50.00"
            keyboardType="decimal-pad"
            maxLength={10}
          />
        </View>

        <TouchableOpacity
          style={styles.sampleButton}
          onPress={generateSampleData}>
          <Text style={styles.sampleButtonText}>Generate Sample Data</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.createButton, isWriting && styles.buttonDisabled]}
          onPress={createStudentCard}
          disabled={isWriting}>
          {isWriting ? (
            <>
              <ActivityIndicator color="white" size="small" />
              <Text style={styles.buttonText}>Writing to Card...</Text>
            </>
          ) : (
            <Text style={styles.buttonText}>Create Student Card</Text>
          )}
        </TouchableOpacity>
      </View>

      <View style={styles.instructions}>
        <Text style={styles.instructionsTitle}>Instructions:</Text>
        <Text style={styles.instructionText}>
          1. Fill in all required fields above
        </Text>
        <Text style={styles.instructionText}>
          2. Tap "Create Student Card" button
        </Text>
        <Text style={styles.instructionText}>
          3. When prompted, place a blank NFC card near your device
        </Text>
        <Text style={styles.instructionText}>
          4. Keep the card in place until the write operation completes
        </Text>
        <Text style={styles.instructionText}>
          5. The card will now contain the student's information
        </Text>
      </View>

      <View style={styles.dataFormat}>
        <Text style={styles.dataFormatTitle}>Data Format on Card:</Text>
        <View style={styles.codeBlock}>
          <Text style={styles.codeText}>
            {JSON.stringify(
              {
                student_id: "S12345",
                name: "John Doe",
                balance: 50.0,
                txn_count: 0,
              },
              null,
              2
            )}
          </Text>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
    padding: 16,
  },
  header: {
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: "#666",
    lineHeight: 22,
  },
  form: {
    backgroundColor: "white",
    padding: 20,
    borderRadius: 12,
    marginBottom: 24,
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
    marginBottom: 8,
  },
  input: {
    borderWidth: 2,
    borderColor: "#e0e0e0",
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: "#fafafa",
  },
  sampleButton: {
    backgroundColor: "#f0f0f0",
    padding: 12,
    borderRadius: 8,
    alignItems: "center",
    marginBottom: 20,
    borderWidth: 1,
    borderColor: "#ddd",
  },
  sampleButtonText: {
    color: "#007AFF",
    fontSize: 14,
    fontWeight: "600",
  },
  createButton: {
    backgroundColor: "#007AFF",
    padding: 16,
    borderRadius: 8,
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "center",
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  buttonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "bold",
    marginLeft: 8,
  },
  instructions: {
    backgroundColor: "white",
    padding: 20,
    borderRadius: 12,
    marginBottom: 24,
  },
  instructionsTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 12,
  },
  instructionText: {
    fontSize: 14,
    color: "#666",
    marginBottom: 8,
    lineHeight: 20,
  },
  dataFormat: {
    backgroundColor: "white",
    padding: 20,
    borderRadius: 12,
    marginBottom: 24,
  },
  dataFormatTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 12,
  },
  codeBlock: {
    backgroundColor: "#f8f8f8",
    padding: 16,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: "#007AFF",
  },
  codeText: {
    fontFamily: "monospace",
    fontSize: 12,
    color: "#333",
    lineHeight: 18,
  },
});

export default StudentCardManager;
