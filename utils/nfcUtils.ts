import { StudentData, Transaction } from "@/types/student";
import AsyncStorage from "@react-native-async-storage/async-storage";

const TRANSACTIONS_KEY = "nfc_transactions";
const TERMINAL_ID_KEY = "terminal_id";

export class TransactionLogger {
  static async logTransaction(transaction: Transaction): Promise<void> {
    try {
      const existingTransactions = await this.getTransactions();
      const updatedTransactions = [...existingTransactions, transaction];
      await AsyncStorage.setItem(
        TRANSACTIONS_KEY,
        JSON.stringify(updatedTransactions)
      );
    } catch (error) {
      console.error("Failed to log transaction:", error);
      throw new Error("Failed to save transaction log");
    }
  }

  static async getTransactions(): Promise<Transaction[]> {
    try {
      const transactions = await AsyncStorage.getItem(TRANSACTIONS_KEY);
      return transactions ? JSON.parse(transactions) : [];
    } catch (error) {
      console.error("Failed to get transactions:", error);
      return [];
    }
  }

  static async getTodaysTransactions(): Promise<Transaction[]> {
    const allTransactions = await this.getTransactions();
    const today = new Date().toDateString();
    return allTransactions.filter(
      (t) => new Date(t.timestamp).toDateString() === today
    );
  }

  static async clearTransactions(): Promise<void> {
    try {
      await AsyncStorage.removeItem(TRANSACTIONS_KEY);
    } catch (error) {
      console.error("Failed to clear transactions:", error);
    }
  }

  static async setTerminalId(terminalId: string): Promise<void> {
    try {
      await AsyncStorage.setItem(TERMINAL_ID_KEY, terminalId);
    } catch (error) {
      console.error("Failed to set terminal ID:", error);
    }
  }

  static async getTerminalId(): Promise<string> {
    try {
      const terminalId = await AsyncStorage.getItem(TERMINAL_ID_KEY);
      return terminalId || "terminal_1";
    } catch (error) {
      console.error("Failed to get terminal ID:", error);
      return "terminal_1";
    }
  }
}

export class NFCErrorHandler {
  static getErrorMessage(error: unknown): string {
    if (!(error instanceof Error)) {
      return "Unknown error occurred";
    }

    const message = error.message.toLowerCase();

    // Common NFC error patterns and user-friendly messages
    if (message.includes("no ndef message found")) {
      return "This card doesn't contain student data. Please use a properly formatted student card or create a new one.";
    }

    if (
      message.includes("tag was lost") ||
      message.includes("tag connection lost")
    ) {
      return "Card was removed too quickly. Please keep the card near your device until the operation completes.";
    }

    if (message.includes("nfc not supported")) {
      return "NFC is not supported on this device.";
    }

    if (message.includes("nfc not enabled")) {
      return "NFC is disabled. Please enable NFC in your device settings.";
    }

    if (message.includes("insufficient balance")) {
      return "Insufficient balance on the card for this transaction.";
    }

    if (message.includes("invalid student data")) {
      return "The card contains invalid or corrupted student data. Please contact support.";
    }

    if (message.includes("timeout") || message.includes("timed out")) {
      return "Operation timed out. Please try again and keep the card close to your device.";
    }

    if (message.includes("cancelled") || message.includes("canceled")) {
      return "Operation was cancelled.";
    }

    if (message.includes("write") && message.includes("failed")) {
      return "Failed to write to card. The card may be read-only or damaged.";
    }

    if (message.includes("read") && message.includes("failed")) {
      return "Failed to read from card. Please try again or use a different card.";
    }

    // Return the original error message if no specific pattern matches
    return error.message;
  }

  static getErrorTitle(error: unknown): string {
    if (!(error instanceof Error)) {
      return "Error";
    }

    const message = error.message.toLowerCase();

    if (message.includes("no ndef message found")) {
      return "Invalid Card";
    }

    if (
      message.includes("tag was lost") ||
      message.includes("tag connection lost")
    ) {
      return "Card Removed";
    }

    if (
      message.includes("nfc not supported") ||
      message.includes("nfc not enabled")
    ) {
      return "NFC Issue";
    }

    if (message.includes("insufficient balance")) {
      return "Insufficient Balance";
    }

    if (message.includes("timeout") || message.includes("timed out")) {
      return "Timeout";
    }

    if (message.includes("write") && message.includes("failed")) {
      return "Write Failed";
    }

    if (message.includes("read") && message.includes("failed")) {
      return "Read Failed";
    }

    return "Error";
  }
}

export class NFCDataHandler {
  static encodeStudentData(data: StudentData): string {
    return JSON.stringify(data);
  }

  static decodeStudentData(encodedData: string): StudentData {
    try {
      const parsed = JSON.parse(encodedData);
      return {
        student_id: parsed.student_id || "",
        name: parsed.name || "",
        balance: parseFloat(parsed.balance) || 0,
        txn_count: parseInt(parsed.txn_count) || 0,
      };
    } catch (error) {
      throw new Error("Invalid student data format");
    }
  }

  static validateStudentData(data: StudentData): boolean {
    return !!(
      data.student_id &&
      data.name &&
      typeof data.balance === "number" &&
      typeof data.txn_count === "number" &&
      data.balance >= 0
    );
  }

  static processTransaction(
    studentData: StudentData,
    amount: number,
    terminalId: string
  ): { updatedData: StudentData; transaction: Transaction } {
    if (studentData.balance < amount) {
      throw new Error("Insufficient balance");
    }

    const oldBalance = studentData.balance;
    const newBalance = oldBalance - amount;
    const newTxnCount = studentData.txn_count + 1;

    const updatedData: StudentData = {
      ...studentData,
      balance: newBalance,
      txn_count: newTxnCount,
    };

    const transaction: Transaction = {
      id: `${terminalId}_${Date.now()}_${studentData.student_id}`,
      timestamp: new Date().toISOString(),
      student_id: studentData.student_id,
      student_name: studentData.name,
      old_balance: oldBalance,
      new_balance: newBalance,
      amount_deducted: amount,
      terminal_id: terminalId,
    };

    return { updatedData, transaction };
  }
}
